/* 简化版Pace.js - 页面加载进度条 */
(function() {
  'use strict';
  
  var Pace = {
    running: false,
    
    start: function() {
      if (this.running) return;
      this.running = true;
      
      var progress = document.createElement('div');
      progress.id = 'pace-progress';
      progress.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        height: 2px;
        background: #007bff;
        z-index: 9999;
        transition: width 0.3s ease;
        width: 0%;
      `;
      
      document.body.appendChild(progress);
      
      var width = 0;
      var interval = setInterval(function() {
        width += Math.random() * 15;
        if (width >= 100) {
          width = 100;
          clearInterval(interval);
          setTimeout(function() {
            Pace.done();
          }, 200);
        }
        progress.style.width = width + '%';
      }, 100);
    },
    
    done: function() {
      this.running = false;
      var progress = document.getElementById('pace-progress');
      if (progress) {
        progress.style.width = '100%';
        setTimeout(function() {
          if (progress.parentNode) {
            progress.parentNode.removeChild(progress);
          }
        }, 300);
      }
    }
  };
  
  // 自动启动
  if (document.readyState === 'loading') {
    Pace.start();
    window.addEventListener('load', function() {
      Pace.done();
    });
  }
  
  window.Pace = Pace;
})();
