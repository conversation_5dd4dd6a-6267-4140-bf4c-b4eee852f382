/* 简化版字体图标CSS */

@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 使用Unicode字符作为图标的替代方案 */
.icon-download:before {
  content: "⬇";
}

.icon-file:before {
  content: "📄";
}

.icon-email:before {
  content: "✉";
}

.icon-success:before {
  content: "✓";
}

.icon-error:before {
  content: "✗";
}

.icon-loading:before {
  content: "⟳";
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
