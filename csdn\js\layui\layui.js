/* 简化版Layui JS - 主要包含表格功能 */
(function(window) {
  'use strict';

  var layui = {
    version: '2.8.0-simplified',
    modules: {},
    
    use: function(modules, callback) {
      if (typeof modules === 'string') {
        modules = [modules];
      }
      
      var loadedModules = {};
      modules.forEach(function(module) {
        if (layui.modules[module]) {
          loadedModules[module] = layui.modules[module];
        }
      });
      
      if (callback) {
        callback.call(layui, loadedModules.table);
      }
      
      return layui;
    },
    
    define: function(name, factory) {
      this.modules[name] = factory();
      return this;
    }
  };

  // 表格模块
  layui.define('table', function() {
    var table = {
      render: function(options) {
        var elem = document.querySelector(options.elem);
        if (!elem) return;
        
        // 存储配置
        elem._layui_config = options;
        
        // 创建表格HTML
        this.createTable(elem, options);
        
        // 加载数据
        this.loadData(elem, options);
        
        return {
          config: options,
          reload: function(newOptions) {
            if (newOptions) {
              Object.assign(options, newOptions);
            }
            table.loadData(elem, options);
          }
        };
      },
      
      createTable: function(elem, options) {
        var cols = options.cols[0];
        var html = '<table class="layui-table">';
        
        // 表头
        html += '<thead><tr>';
        cols.forEach(function(col) {
          var width = col.width ? ' style="width:' + col.width + '"' : '';
          html += '<th' + width + '>' + (col.title || '') + '</th>';
        });
        html += '</tr></thead>';
        
        // 表体
        html += '<tbody id="' + elem.id + '-tbody"></tbody>';
        html += '</table>';
        
        // 分页
        if (options.page) {
          html += '<div class="layui-table-page" id="' + elem.id + '-page"></div>';
        }
        
        elem.innerHTML = html;
      },
      
      loadData: function(elem, options) {
        var tbody = elem.querySelector('tbody');
        if (!tbody) return;
        
        if (options.url) {
          // AJAX加载数据
          var requestData = Object.assign({}, options.where || {});
          if (options.page) {
            requestData[options.request.pageName || 'page'] = options.page.curr || 1;
            requestData[options.request.limitName || 'limit'] = options.limit || 10;
          }
          
          var xhr = new XMLHttpRequest();
          xhr.open(options.method || 'GET', options.url, true);
          
          if (options.contentType) {
            xhr.setRequestHeader('Content-Type', options.contentType);
          }
          
          xhr.onload = function() {
            if (xhr.status === 200) {
              var response = JSON.parse(xhr.responseText);
              if (options.parseData) {
                response = options.parseData(response);
              }
              table.renderData(elem, options, response.data || []);
              if (options.page) {
                table.renderPage(elem, options, response.count || 0);
              }
            }
          };
          
          if (options.method === 'POST') {
            xhr.send(JSON.stringify(requestData));
          } else {
            xhr.send();
          }
        } else if (options.data) {
          // 直接使用数据
          this.renderData(elem, options, options.data);
        }
      },
      
      renderData: function(elem, options, data) {
        var tbody = elem.querySelector('tbody');
        var cols = options.cols[0];
        var html = '';
        
        data.forEach(function(row, index) {
          html += '<tr>';
          cols.forEach(function(col) {
            var value = row[col.field] || '';
            if (col.templet && typeof col.templet === 'function') {
              value = col.templet(row);
            }
            var align = col.align ? ' style="text-align:' + col.align + '"' : '';
            html += '<td' + align + '>' + value + '</td>';
          });
          html += '</tr>';
        });
        
        tbody.innerHTML = html;
        
        // 绑定事件
        this.bindEvents(elem, options, data);
      },
      
      bindEvents: function(elem, options, data) {
        var tbody = elem.querySelector('tbody');
        tbody.addEventListener('click', function(e) {
          var target = e.target;
          if (target.tagName === 'BUTTON' && target.hasAttribute('lay-event')) {
            var tr = target.closest('tr');
            var rowIndex = Array.from(tbody.children).indexOf(tr);
            var rowData = data[rowIndex];
            var event = target.getAttribute('lay-event');
            
            // 触发工具条事件
            if (table.eventHandlers[options.elem]) {
              table.eventHandlers[options.elem].forEach(function(handler) {
                handler({
                  event: event,
                  data: rowData,
                  tr: tr
                });
              });
            }
          }
        });
      },
      
      renderPage: function(elem, options, total) {
        var pageElem = elem.querySelector('.layui-table-page');
        if (!pageElem) return;
        
        var curr = options.page.curr || 1;
        var limit = options.limit || 10;
        var totalPage = Math.ceil(total / limit);
        
        var html = '<div class="layui-laypage">';
        
        // 上一页
        if (curr > 1) {
          html += '<a href="#" data-page="' + (curr - 1) + '">上一页</a>';
        }
        
        // 页码
        for (var i = 1; i <= totalPage; i++) {
          if (i === curr) {
            html += '<span class="layui-laypage-curr">' + i + '</span>';
          } else {
            html += '<a href="#" data-page="' + i + '">' + i + '</a>';
          }
        }
        
        // 下一页
        if (curr < totalPage) {
          html += '<a href="#" data-page="' + (curr + 1) + '">下一页</a>';
        }
        
        html += '</div>';
        pageElem.innerHTML = html;
        
        // 绑定分页事件
        pageElem.addEventListener('click', function(e) {
          e.preventDefault();
          var target = e.target;
          if (target.tagName === 'A' && target.hasAttribute('data-page')) {
            var page = parseInt(target.getAttribute('data-page'));
            options.page.curr = page;
            table.loadData(elem, options);
          }
        });
      },
      
      eventHandlers: {},
      
      on: function(filter, callback) {
        var elem = '[lay-filter="' + filter + '"]';
        if (!this.eventHandlers[elem]) {
          this.eventHandlers[elem] = [];
        }
        this.eventHandlers[elem].push(callback);
      },
      
      reload: function(id, options) {
        var elem = document.querySelector('#' + id);
        if (elem && elem._layui_config) {
          Object.assign(elem._layui_config, options);
          this.loadData(elem, elem._layui_config);
        }
      }
    };
    
    return table;
  });

  // 导出到全局
  window.layui = layui;
  
})(window);
