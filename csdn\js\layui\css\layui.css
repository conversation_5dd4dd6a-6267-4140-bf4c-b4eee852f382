/* 简化版Layui CSS - 仅包含表格相关样式 */

.layui-table {
  width: 100%;
  background-color: #fff;
  color: #666;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #e6e6e6;
}

.layui-table th,
.layui-table td {
  padding: 9px 15px;
  border: 1px solid #e6e6e6;
  text-align: left;
  font-size: 14px;
}

.layui-table th {
  background-color: #f2f2f2;
  font-weight: 400;
  color: #333;
}

.layui-table tbody tr:hover {
  background-color: #f8f8f8;
}

.layui-table-box {
  position: relative;
  overflow: auto;
}

.layui-table-page {
  margin: 10px 0;
  text-align: center;
}

.layui-btn {
  display: inline-block;
  height: 32px;
  line-height: 32px;
  padding: 0 18px;
  background-color: #009688;
  color: #fff;
  white-space: nowrap;
  text-align: center;
  font-size: 14px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s;
}

.layui-btn:hover {
  opacity: 0.8;
}

.layui-btn-primary {
  background-color: #fff;
  color: #666;
  border: 1px solid #c9c9c9;
}

.layui-btn-normal {
  background-color: #1e9fff;
}

.layui-btn-warm {
  background-color: #ffb800;
}

.layui-btn-danger {
  background-color: #ff5722;
}

.layui-btn-sm {
  height: 28px;
  line-height: 28px;
  padding: 0 10px;
  font-size: 12px;
}

.layui-btn-xs {
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  font-size: 12px;
}

.layui-badge {
  display: inline-block;
  padding: 2px 6px;
  font-size: 12px;
  color: #fff;
  background-color: #ff5722;
  border-radius: 2px;
}

.layui-bg-blue {
  background-color: #1e9fff !important;
}

.layui-bg-green {
  background-color: #5fb878 !important;
}

.layui-bg-orange {
  background-color: #ffb800 !important;
}

.layui-bg-red {
  background-color: #ff5722 !important;
}

.layui-bg-cyan {
  background-color: #2f4056 !important;
}

.layui-bg-gray {
  background-color: #c2c2c2 !important;
}

/* 分页样式 */
.layui-laypage {
  display: inline-block;
  vertical-align: middle;
  border: 1px solid #e2e2e2;
  border-radius: 2px;
  background-color: #fff;
}

.layui-laypage a,
.layui-laypage span {
  display: inline-block;
  padding: 0 15px;
  height: 28px;
  line-height: 28px;
  border-right: 1px solid #e2e2e2;
  text-decoration: none;
  color: #333;
}

.layui-laypage a:hover {
  background-color: #f2f2f2;
}

.layui-laypage .layui-laypage-curr {
  background-color: #009688;
  color: #fff;
}

/* 表单样式 */
.layui-form-item {
  margin-bottom: 15px;
  clear: both;
}

.layui-form-label {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: 80px;
  padding: 9px 15px;
  height: 38px;
  line-height: 20px;
  text-align: right;
  color: #333;
  font-weight: 400;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
}

.layui-input-block {
  margin-left: 110px;
  min-height: 36px;
}

.layui-input {
  display: block;
  width: 100%;
  height: 38px;
  padding: 5px 10px;
  line-height: 1.3;
  border: 1px solid #e6e6e6;
  background-color: #fff;
  border-radius: 2px;
  color: #666;
  transition: all 0.3s;
}

.layui-input:focus {
  border-color: #5fb878 !important;
}
