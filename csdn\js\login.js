/* 登录相关功能 */
(function() {
  'use strict';
  
  // 模拟登录功能
  window.getLoginType = function() {
    // 检查是否已有token
    var token = localStorage.getItem('token');
    if (token) {
      return;
    }
    
    // 显示登录弹窗
    layer.open({
      type: 1,
      title: '需要登录',
      content: document.getElementById('loginLayer').innerHTML,
      area: ['400px', '500px'],
      btn: ['确定', '取消'],
      yes: function(index, layero) {
        // 模拟登录成功
        var token = 'demo_token_' + Date.now();
        localStorage.setItem('token', token);
        layer.msg('登录成功！', {icon: 1});
        layer.close(index);
      }
    });
  };
  
  // 检查登录状态
  window.checkLoginStatus = function() {
    var token = localStorage.getItem('token');
    return !!token;
  };
  
  // 退出登录
  window.logout = function() {
    localStorage.removeItem('token');
    layer.msg('已退出登录', {icon: 1});
    location.reload();
  };
  
  // 页面加载时自动设置token（测试用）
  document.addEventListener('DOMContentLoaded', function() {
    if (!localStorage.getItem('token')) {
      localStorage.setItem('token', 'demo_token_' + Date.now());
    }
  });
  
})();
