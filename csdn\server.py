#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN测试服务器
提供CSDN文章下载功能的后端API
"""

import os
import json
import time
import logging
import tempfile
import threading
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 模拟数据库 - 存储任务信息
tasks_db = {}
task_id_counter = 1

# 模拟用户token验证
def verify_token(token):
    """验证token"""
    if not token:
        return False
    # 简单验证：token以demo_token_开头
    return token.startswith('demo_token_') or token == 'test_token'

def generate_task_id():
    """生成任务ID"""
    global task_id_counter
    task_id = task_id_counter
    task_id_counter += 1
    return task_id

def simulate_csdn_parsing(url):
    """模拟CSDN文章解析"""
    time.sleep(2)  # 模拟解析时间
    
    # 模拟解析结果
    return {
        'title': f'CSDN文章标题 - {url.split("/")[-1] if "/" in url else "示例"}',
        'author': '示例作者',
        'content': f'''
        <h1>这是一个示例CSDN文章</h1>
        <p>原文链接：{url}</p>
        <p>这是文章的主要内容。在实际应用中，这里会是从CSDN解析出来的完整文章内容。</p>
        <h2>技术要点</h2>
        <ul>
            <li>Python编程</li>
            <li>Web爬虫技术</li>
            <li>数据处理</li>
        </ul>
        <h2>代码示例</h2>
        <pre><code>
def hello_world():
    print("Hello, CSDN!")
    return "Success"
        </code></pre>
        <p>以上就是本文的主要内容。</p>
        ''',
        'publish_time': '2024-01-01 12:00:00',
        'read_count': 1234
    }

def generate_html_file(article_data, url):
    """生成HTML文件"""
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{article_data['title']}</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1000px;
                margin: 0 auto;
                padding: 20px;
                background-color: #fff;
            }}
            .header {{
                border-bottom: 2px solid #007acc;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }}
            .title {{
                font-size: 28px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
            }}
            .meta {{
                color: #666;
                font-size: 14px;
                margin-bottom: 10px;
            }}
            .content {{
                font-size: 16px;
                line-height: 1.8;
                color: #333;
            }}
            .content h1, .content h2, .content h3 {{
                color: #333;
                margin-top: 30px;
                margin-bottom: 15px;
            }}
            .content pre {{
                background-color: #f6f8fa;
                border: 1px solid #e1e4e8;
                border-radius: 6px;
                padding: 16px;
                overflow-x: auto;
                font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            }}
            .footer {{
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 12px;
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1 class="title">{article_data['title']}</h1>
            <div class="meta">
                <span>作者：{article_data['author']}</span> | 
                <span>发布时间：{article_data['publish_time']}</span> | 
                <span>阅读量：{article_data['read_count']}</span>
            </div>
            <div class="meta">
                <span>原文链接：<a href="{url}" target="_blank">{url}</a></span>
            </div>
        </div>
        <div class="content">
            {article_data['content']}
        </div>
        <div class="footer">
            <p>本文档由CSDN测试工具生成 | 生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </body>
    </html>
    """
    
    # 保存到临时文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"csdn_article_{timestamp}.html"
    filepath = os.path.join(tempfile.gettempdir(), filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return filepath

def simulate_email_sending(email, filepath, article_title):
    """模拟邮件发送"""
    time.sleep(1)  # 模拟发送时间
    logger.info(f"模拟发送邮件到 {email}，文件：{filepath}，标题：{article_title}")
    return True

def process_article_task(task_id):
    """后台处理文章任务"""
    try:
        task = tasks_db.get(task_id)
        if not task:
            return
        
        # 更新任务状态为处理中
        task['status'] = 1
        task['started_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        logger.info(f"开始处理任务 {task_id}: {task['url']}")
        
        # 模拟解析文章
        article_data = simulate_csdn_parsing(task['url'])
        task['article_data'] = article_data
        
        # 生成HTML文件
        filepath = generate_html_file(article_data, task['url'])
        task['output_file_path'] = filepath
        
        # 模拟发送邮件
        success = simulate_email_sending(task['email'], filepath, article_data['title'])
        
        if success:
            task['status'] = 3  # 完成
            task['completed_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            logger.info(f"任务 {task_id} 处理完成")
        else:
            task['status'] = 2  # 错误
            task['error_message'] = "邮件发送失败"
            logger.error(f"任务 {task_id} 邮件发送失败")
            
    except Exception as e:
        logger.error(f"处理任务 {task_id} 失败: {e}")
        if task_id in tasks_db:
            tasks_db[task_id]['status'] = 2
            tasks_db[task_id]['error_message'] = str(e)

@app.route('/article/downloadnew', methods=['POST'])
def download_article_new():
    """文章下载接口"""
    try:
        data = request.json
        if not data:
            return jsonify({"code": 400, "msg": "请求数据格式错误"}), 400
        
        # 获取参数
        link = data.get('link')
        file_link = data.get('fileLink')
        email = data.get('email')
        format_type = data.get('format', 'html')
        token = data.get('token')
        
        # 验证参数
        if not (link or file_link):
            return jsonify({"code": 400, "msg": "请提供文章链接或文件链接"}), 400
        
        if not email:
            return jsonify({"code": 400, "msg": "请提供收件邮箱"}), 400
        
        if not verify_token(token):
            return jsonify({"code": 401, "msg": "请先登录"}), 401
        
        # 确定任务类型和URL
        if file_link:
            task_type = 1  # 文件下载
            url = file_link
            task_name = f"CSDN文件下载 - {file_link.split('/')[-1]}"
        else:
            task_type = 0  # 文章浏览
            url = link
            task_name = f"CSDN文章 - {link.split('/')[-1]}"
        
        # 创建任务
        task_id = generate_task_id()
        task = {
            'id': task_id,
            'taskName': task_name,
            'taskType': task_type,
            'status': 0 if task_type == 0 else -1,  # 文章浏览直接开始，文件下载需要付费
            'url': url,
            'email': email,
            'formatType': format_type,
            'token': token,
            'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'paymentRequired': task_type == 1,
            'paymentAmount': 2.00 if task_type == 1 else 0.00,
            'retryCount': 0
        }
        
        tasks_db[task_id] = task
        
        # 如果是文章浏览，立即开始处理
        if task_type == 0:
            thread = threading.Thread(target=process_article_task, args=(task_id,))
            thread.daemon = True
            thread.start()
        
        return jsonify({
            "code": 0,
            "msg": "任务已提交" + ("，请1-3分钟后检查您的邮箱" if task_type == 0 else "，付费后自动开始下载"),
            "data": {"task_id": task_id}
        })
        
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}"}), 500

@app.route('/csdn/article/csdnTaskDownloadHistorylist', methods=['POST'])
def get_task_history():
    """获取任务历史记录"""
    try:
        data = request.json
        if not data:
            return jsonify({"code": 400, "msg": "请求数据格式错误"}), 400
        
        token = data.get('token')
        title = data.get('title', '')
        page = data.get('page', 1)
        page_size = data.get('pageSize', 10)
        
        if not verify_token(token):
            return jsonify({"code": 401, "msg": "请先登录"}), 401
        
        # 过滤用户的任务
        user_tasks = [task for task in tasks_db.values() if task.get('token') == token]
        
        # 标题过滤
        if title:
            user_tasks = [task for task in user_tasks if title.lower() in task.get('taskName', '').lower()]
        
        # 排序（按创建时间倒序）
        user_tasks.sort(key=lambda x: x.get('createTime', ''), reverse=True)
        
        # 分页
        total = len(user_tasks)
        start = (page - 1) * page_size
        end = start + page_size
        records = user_tasks[start:end]
        
        return jsonify({
            "code": 0,
            "msg": "查询成功",
            "data": {
                "records": records,
                "total": total,
                "page": page,
                "pageSize": page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取任务历史失败: {e}")
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}"}), 500

@app.route('/csdn/article/retryTask', methods=['POST'])
def retry_task():
    """重试任务"""
    try:
        data = request.json
        if not data:
            return jsonify({"code": 400, "msg": "请求数据格式错误"}), 400
        
        token = data.get('token')
        task_id = data.get('id')
        
        if not verify_token(token):
            return jsonify({"code": 401, "msg": "请先登录"}), 401
        
        if not task_id:
            return jsonify({"code": 400, "msg": "请提供任务ID"}), 400
        
        # 查找任务
        task = tasks_db.get(task_id)
        if not task or task.get('token') != token:
            return jsonify({"code": 404, "msg": "任务不存在"}), 404
        
        # 重置任务状态
        task['status'] = 0
        task['error_message'] = None
        task['retryCount'] = task.get('retryCount', 0) + 1
        
        # 启动后台处理
        thread = threading.Thread(target=process_article_task, args=(task_id,))
        thread.daemon = True
        thread.start()
        
        return jsonify({"code": 0, "msg": "任务已重新提交"})
        
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}"}), 500

@app.route('/payment/getQRCode', methods=['POST'])
def get_payment_qr_code():
    """获取支付二维码"""
    try:
        data = request.json
        if not data:
            return jsonify({"code": 400, "msg": "请求数据格式错误"}), 400
        
        token = data.get('token')
        task_id = data.get('id')
        
        if not verify_token(token):
            return jsonify({"code": 401, "msg": "请先登录"}), 401
        
        # 查找任务
        task = tasks_db.get(task_id)
        if not task or task.get('token') != token:
            return jsonify({"code": 404, "msg": "任务不存在"}), 404
        
        # 生成模拟二维码（1x1像素的透明PNG）
        mock_qr_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        return jsonify({
            "code": 0,
            "msg": "二维码生成成功",
            "data": mock_qr_base64
        })
        
    except Exception as e:
        logger.error(f"生成支付二维码失败: {e}")
        return jsonify({"code": 500, "msg": f"服务器错误: {str(e)}"}), 500

# 静态文件服务
@app.route('/')
def index():
    """首页"""
    return send_from_directory('.', 'csdn-test.html')

@app.route('/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('.', filename)

if __name__ == '__main__':
    print("=" * 50)
    print("CSDN测试服务器启动中...")
    print("请访问 http://127.0.0.1:5000 使用测试页面")
    print("=" * 50)
    app.run(host='0.0.0.0', port=5000, debug=True)
