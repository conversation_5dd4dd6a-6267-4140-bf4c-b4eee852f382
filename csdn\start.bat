@echo off
echo ========================================
echo CSDN测试系统启动脚本
echo ========================================
echo.

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
pip show flask >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已安装
)

echo.
echo 启动CSDN测试服务器...
echo 请访问 http://127.0.0.1:5000 使用系统
echo 按 Ctrl+C 停止服务器
echo.

python server.py

pause
