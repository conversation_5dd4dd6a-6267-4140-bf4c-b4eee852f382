/* 简化版jQuery - 仅包含基本功能 */
(function(window) {
  function $(selector) {
    if (typeof selector === 'string') {
      return new jQueryLite(document.querySelectorAll(selector));
    } else if (selector.nodeType) {
      return new jQueryLite([selector]);
    } else if (typeof selector === 'function') {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', selector);
      } else {
        selector();
      }
      return;
    }
    return new jQueryLite(selector);
  }

  function jQueryLite(elements) {
    this.elements = Array.from(elements || []);
    this.length = this.elements.length;
  }

  jQueryLite.prototype = {
    ready: function(callback) {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', callback);
      } else {
        callback();
      }
      return this;
    },

    click: function(handler) {
      this.elements.forEach(el => {
        if (handler) {
          el.addEventListener('click', handler);
        } else {
          el.click();
        }
      });
      return this;
    },

    change: function(handler) {
      this.elements.forEach(el => {
        el.addEventListener('change', handler);
      });
      return this;
    },

    val: function(value) {
      if (value !== undefined) {
        this.elements.forEach(el => el.value = value);
        return this;
      }
      return this.elements[0] ? this.elements[0].value : '';
    },

    text: function(text) {
      if (text !== undefined) {
        this.elements.forEach(el => el.textContent = text);
        return this;
      }
      return this.elements[0] ? this.elements[0].textContent : '';
    },

    html: function(html) {
      if (html !== undefined) {
        this.elements.forEach(el => el.innerHTML = html);
        return this;
      }
      return this.elements[0] ? this.elements[0].innerHTML : '';
    },

    addClass: function(className) {
      this.elements.forEach(el => el.classList.add(className));
      return this;
    },

    removeClass: function(className) {
      this.elements.forEach(el => el.classList.remove(className));
      return this;
    },

    show: function() {
      this.elements.forEach(el => el.style.display = '');
      return this;
    },

    hide: function() {
      this.elements.forEach(el => el.style.display = 'none');
      return this;
    },

    attr: function(name, value) {
      if (value !== undefined) {
        this.elements.forEach(el => el.setAttribute(name, value));
        return this;
      }
      return this.elements[0] ? this.elements[0].getAttribute(name) : null;
    },

    is: function(selector) {
      return this.elements[0] ? this.elements[0].matches(selector) : false;
    },

    trim: function() {
      return this.val().trim();
    }
  };

  // AJAX功能
  $.ajax = function(options) {
    const xhr = new XMLHttpRequest();
    const method = (options.type || options.method || 'GET').toUpperCase();
    const url = options.url;
    const data = options.data;
    const timeout = options.timeout || 30000;

    xhr.open(method, url, true);

    if (options.contentType) {
      xhr.setRequestHeader('Content-Type', options.contentType);
    }

    if (options.headers) {
      Object.keys(options.headers).forEach(key => {
        xhr.setRequestHeader(key, options.headers[key]);
      });
    }

    xhr.timeout = timeout;

    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        let response = xhr.responseText;
        try {
          response = JSON.parse(response);
        } catch (e) {}
        if (options.success) options.success(response);
      } else {
        if (options.error) options.error(xhr, 'error');
      }
    };

    xhr.onerror = function() {
      if (options.error) options.error(xhr, 'error');
    };

    xhr.ontimeout = function() {
      if (options.error) options.error(xhr, 'timeout');
    };

    if (method === 'POST' && data) {
      xhr.send(data);
    } else {
      xhr.send();
    }
  };

  // 扩展$对象
  $.extend = function(target, source) {
    Object.keys(source).forEach(key => {
      target[key] = source[key];
    });
    return target;
  };

  window.$ = window.jQuery = $;

  // 文档就绪
  $(function() {
    // 初始化代码
  });

})(window);
