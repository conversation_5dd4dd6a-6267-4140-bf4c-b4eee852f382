/* 简化版Bootstrap JS */
(function() {
  'use strict';

  // 基本的Bootstrap功能
  window.bootstrap = {
    version: '5.0.0-simplified'
  };

  // 模态框功能
  function Modal(element, options) {
    this.element = element;
    this.options = options || {};
  }

  Modal.prototype.show = function() {
    this.element.style.display = 'block';
    this.element.classList.add('show');
  };

  Modal.prototype.hide = function() {
    this.element.style.display = 'none';
    this.element.classList.remove('show');
  };

  // 下拉菜单功能
  function Dropdown(element) {
    this.element = element;
  }

  Dropdown.prototype.toggle = function() {
    const menu = this.element.nextElementSibling;
    if (menu) {
      menu.classList.toggle('show');
    }
  };

  // 工具提示功能
  function Tooltip(element, options) {
    this.element = element;
    this.options = options || {};
  }

  Tooltip.prototype.show = function() {
    // 简单的工具提示实现
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = this.element.getAttribute('title');
    document.body.appendChild(tooltip);
  };

  // 导出到全局
  window.Modal = Modal;
  window.Dropdown = Dropdown;
  window.Tooltip = Tooltip;

  // 自动初始化
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化下拉菜单
    document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(element) {
      element.addEventListener('click', function(e) {
        e.preventDefault();
        new Dropdown(this).toggle();
      });
    });

    // 初始化模态框
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(function(element) {
      element.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('data-bs-target'));
        if (target) {
          new Modal(target).show();
        }
      });
    });

    // 关闭模态框
    document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(function(element) {
      element.addEventListener('click', function() {
        const modal = this.closest('.modal');
        if (modal) {
          new Modal(modal).hide();
        }
      });
    });
  });

})();
