<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSDN测试页面 - 简化版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="email"], select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="email"]:focus, select:focus {
            border-color: #007bff;
            outline: none;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            cursor: pointer;
        }
        
        .radio-group input[type="radio"] {
            margin-right: 8px;
            width: auto;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .hidden {
            display: none;
        }
        
        .task-list {
            margin-top: 30px;
        }
        
        .task-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .task-status {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .status-0 { background: #cce5ff; color: #0066cc; }
        .status-1 { background: #ffffcc; color: #cc9900; }
        .status-2 { background: #ffcccc; color: #cc0000; }
        .status-3 { background: #ccffcc; color: #009900; }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕷️ CSDN文章浏览/资源下载测试</h1>
        
        <form id="downloadForm">
            <div class="form-group">
                <label>功能选择：</label>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="inputType" value="article" checked>
                        📄 文章浏览
                    </label>
                    <label>
                        <input type="radio" name="inputType" value="file">
                        📁 文件下载
                    </label>
                </div>
            </div>
            
            <div class="alert alert-warning hidden" id="fileWarning">
                <strong>注意：</strong>文件下载功能需要付费（2元），推荐使用免费的文章浏览功能！
            </div>
            
            <div class="form-group">
                <label for="url">链接地址：</label>
                <input type="text" id="url" placeholder="请输入CSDN文章或文件链接" required>
            </div>
            
            <div class="form-group">
                <label for="email">收件邮箱：</label>
                <input type="email" id="email" placeholder="请输入接收邮箱地址" required>
            </div>
            
            <div class="form-group" id="formatGroup">
                <label for="format">输出格式：</label>
                <select id="format">
                    <option value="html">HTML格式</option>
                    <option value="markdown">Markdown格式</option>
                </select>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn" id="submitBtn">
                    🚀 开始处理
                </button>
            </div>
        </form>
        
        <div id="message"></div>
        
        <div class="task-list" id="taskList">
            <h3>📋 任务历史</h3>
            <div id="tasks">
                <div class="loading">正在加载任务列表...</div>
            </div>
        </div>
    </div>

    <script>
        // 简化的工具函数
        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="alert alert-${type}">${text}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
        
        function setLoading(loading) {
            const btn = document.getElementById('submitBtn');
            if (loading) {
                btn.disabled = true;
                btn.textContent = '⏳ 处理中...';
            } else {
                btn.disabled = false;
                btn.textContent = '🚀 开始处理';
            }
        }
        
        // 初始化token
        if (!localStorage.getItem('token')) {
            localStorage.setItem('token', 'demo_token_' + Date.now());
        }
        
        // 功能切换
        document.querySelectorAll('input[name="inputType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const fileWarning = document.getElementById('fileWarning');
                const formatGroup = document.getElementById('formatGroup');
                const urlInput = document.getElementById('url');
                
                if (this.value === 'file') {
                    fileWarning.classList.remove('hidden');
                    formatGroup.classList.add('hidden');
                    urlInput.placeholder = '请输入CSDN文件下载链接';
                } else {
                    fileWarning.classList.add('hidden');
                    formatGroup.classList.remove('hidden');
                    urlInput.placeholder = '请输入CSDN文章链接';
                }
            });
        });
        
        // 表单提交
        document.getElementById('downloadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const inputType = formData.get('inputType');
            const url = document.getElementById('url').value.trim();
            const email = document.getElementById('email').value.trim();
            const format = document.getElementById('format').value;
            
            // 验证
            if (!url) {
                showMessage('请输入链接地址', 'error');
                return;
            }
            
            if (!email) {
                showMessage('请输入邮箱地址', 'error');
                return;
            }
            
            if (!url.startsWith('http')) {
                showMessage('链接地址应该以http开头', 'error');
                return;
            }
            
            // 构建请求数据
            const requestData = {
                email: email,
                token: localStorage.getItem('token'),
                format: format
            };
            
            if (inputType === 'article') {
                requestData.link = url;
            } else {
                requestData.fileLink = url;
            }
            
            // 发送请求
            setLoading(true);
            
            fetch('/article/downloadnew', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);
                
                if (data.code === 0) {
                    showMessage(data.msg || '任务提交成功！', 'success');
                    loadTasks(); // 重新加载任务列表
                } else {
                    showMessage(data.msg || '请求失败', 'error');
                }
            })
            .catch(error => {
                setLoading(false);
                showMessage('网络错误：' + error.message, 'error');
            });
        });
        
        // 加载任务列表
        function loadTasks() {
            const tasksDiv = document.getElementById('tasks');
            
            fetch('/csdn/article/csdnTaskDownloadHistorylist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: localStorage.getItem('token'),
                    page: 1,
                    pageSize: 10
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 0 && data.data.records.length > 0) {
                    const tasks = data.data.records;
                    let html = '';
                    
                    tasks.forEach(task => {
                        const statusText = getStatusText(task.status);
                        const statusClass = `status-${task.status}`;
                        
                        html += `
                            <div class="task-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong>${task.taskName}</strong>
                                        <br>
                                        <small>📧 ${task.email} | ⏰ ${task.createTime}</small>
                                    </div>
                                    <span class="task-status ${statusClass}">${statusText}</span>
                                </div>
                            </div>
                        `;
                    });
                    
                    tasksDiv.innerHTML = html;
                } else {
                    tasksDiv.innerHTML = '<div class="loading">暂无任务记录</div>';
                }
            })
            .catch(error => {
                tasksDiv.innerHTML = '<div class="loading">加载失败：' + error.message + '</div>';
            });
        }
        
        function getStatusText(status) {
            const statusMap = {
                '-1': '待付费',
                '0': '准备开始',
                '1': '正在处理',
                '2': '处理失败',
                '3': '处理完成',
                '4': '已取消'
            };
            return statusMap[status] || '未知状态';
        }
        
        // 页面加载完成后加载任务列表
        document.addEventListener('DOMContentLoaded', function() {
            loadTasks();
            
            // 每30秒自动刷新任务列表
            setInterval(loadTasks, 30000);
        });
    </script>
</body>
</html>
