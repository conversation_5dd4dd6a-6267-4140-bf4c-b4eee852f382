/* 简化版Layer弹窗插件 */
(function(window) {
  'use strict';

  var layer = {
    index: 1000,
    
    // 显示消息
    msg: function(content, options) {
      options = options || {};
      var icon = options.icon || 0;
      var time = options.time || 3000;
      
      var msgBox = document.createElement('div');
      msgBox.className = 'layer-msg';
      msgBox.innerHTML = '<div class="layer-msg-content">' + 
        '<span class="layer-msg-icon layer-msg-icon-' + icon + '"></span>' +
        '<span class="layer-msg-text">' + content + '</span>' +
        '</div>';
      
      // 添加样式
      msgBox.style.cssText = `
        position: fixed;
        top: 50px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: ${this.index++};
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
      `;
      
      document.body.appendChild(msgBox);
      
      // 自动关闭
      setTimeout(function() {
        if (msgBox.parentNode) {
          msgBox.parentNode.removeChild(msgBox);
        }
      }, time);
      
      return this.index - 1;
    },
    
    // 显示加载层
    load: function(type, options) {
      options = options || {};
      var shade = options.shade || [0.3, '#000'];
      
      var loadBox = document.createElement('div');
      loadBox.className = 'layer-load';
      loadBox.innerHTML = '<div class="layer-load-content">' +
        '<div class="layer-load-spinner"></div>' +
        '</div>';
      
      // 添加样式
      loadBox.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(${shade[1] === '#000' ? '0,0,0' : '255,255,255'}, ${shade[0]});
        z-index: ${this.index++};
        display: flex;
        align-items: center;
        justify-content: center;
      `;
      
      var spinner = loadBox.querySelector('.layer-load-spinner');
      spinner.style.cssText = `
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid #fff;
        border-radius: 50%;
        animation: layer-spin 1s linear infinite;
      `;
      
      // 添加动画样式
      if (!document.querySelector('#layer-style')) {
        var style = document.createElement('style');
        style.id = 'layer-style';
        style.textContent = `
          @keyframes layer-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          .layer-msg-icon-0:before { content: "ℹ"; color: #1e9fff; }
          .layer-msg-icon-1:before { content: "✓"; color: #5fb878; }
          .layer-msg-icon-2:before { content: "✗"; color: #ff5722; }
          .layer-msg-icon { margin-right: 5px; font-weight: bold; }
        `;
        document.head.appendChild(style);
      }
      
      document.body.appendChild(loadBox);
      
      return this.index - 1;
    },
    
    // 关闭层
    close: function(index) {
      var layers = document.querySelectorAll('.layer-load, .layer-msg, .layer-dialog');
      layers.forEach(function(layer) {
        if (layer.parentNode) {
          layer.parentNode.removeChild(layer);
        }
      });
    },
    
    // 确认对话框
    confirm: function(content, callback, options) {
      options = options || {};
      
      var dialog = document.createElement('div');
      dialog.className = 'layer-dialog';
      dialog.innerHTML = `
        <div class="layer-dialog-content">
          <div class="layer-dialog-header">
            <h4>确认</h4>
          </div>
          <div class="layer-dialog-body">
            <p>${content}</p>
          </div>
          <div class="layer-dialog-footer">
            <button class="layer-btn layer-btn-primary" onclick="layer.confirmOk(${this.index})">确定</button>
            <button class="layer-btn layer-btn-default" onclick="layer.confirmCancel(${this.index})">取消</button>
          </div>
        </div>
        <div class="layer-dialog-shade"></div>
      `;
      
      // 添加样式
      dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: ${this.index++};
        display: flex;
        align-items: center;
        justify-content: center;
      `;
      
      var content_div = dialog.querySelector('.layer-dialog-content');
      content_div.style.cssText = `
        background: white;
        border-radius: 4px;
        min-width: 300px;
        max-width: 500px;
        position: relative;
        z-index: 1;
      `;
      
      var shade = dialog.querySelector('.layer-dialog-shade');
      shade.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.3);
      `;
      
      // 存储回调
      this.confirmCallback = callback;
      
      document.body.appendChild(dialog);
      
      return this.index - 1;
    },
    
    confirmOk: function(index) {
      if (this.confirmCallback) {
        this.confirmCallback(index);
      }
      this.close(index);
    },
    
    confirmCancel: function(index) {
      this.close(index);
    },
    
    // 输入框
    prompt: function(options, callback) {
      if (typeof options === 'object') {
        var content = options.value || '';
        var title = options.title || '输入';
        
        var dialog = document.createElement('div');
        dialog.className = 'layer-dialog';
        dialog.innerHTML = `
          <div class="layer-dialog-content">
            <div class="layer-dialog-header">
              <h4>${title}</h4>
            </div>
            <div class="layer-dialog-body">
              <textarea style="width:100%;height:100px;border:1px solid #ccc;padding:5px;">${content}</textarea>
            </div>
            <div class="layer-dialog-footer">
              <button class="layer-btn layer-btn-primary" onclick="layer.promptOk(${this.index})">确定</button>
              <button class="layer-btn layer-btn-default" onclick="layer.promptCancel(${this.index})">取消</button>
            </div>
          </div>
          <div class="layer-dialog-shade"></div>
        `;
        
        dialog.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: ${this.index++};
          display: flex;
          align-items: center;
          justify-content: center;
        `;
        
        var content_div = dialog.querySelector('.layer-dialog-content');
        content_div.style.cssText = `
          background: white;
          border-radius: 4px;
          min-width: 400px;
          max-width: 600px;
          position: relative;
          z-index: 1;
          padding: 20px;
        `;
        
        var shade = dialog.querySelector('.layer-dialog-shade');
        shade.style.cssText = `
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0,0,0,0.3);
        `;
        
        this.promptCallback = callback;
        
        document.body.appendChild(dialog);
        
        return this.index - 1;
      }
    },
    
    promptOk: function(index) {
      var textarea = document.querySelector('.layer-dialog textarea');
      if (this.promptCallback && textarea) {
        this.promptCallback(textarea.value, index);
      }
      this.close(index);
    },
    
    promptCancel: function(index) {
      this.close(index);
    },
    
    // 打开层
    open: function(options) {
      var dialog = document.createElement('div');
      dialog.className = 'layer-dialog';
      dialog.innerHTML = `
        <div class="layer-dialog-content">
          <div class="layer-dialog-header">
            <h4>${options.title || '提示'}</h4>
            <button class="layer-close" onclick="layer.close(${this.index})">&times;</button>
          </div>
          <div class="layer-dialog-body">
            ${options.content || ''}
          </div>
          ${options.btn ? '<div class="layer-dialog-footer"><button class="layer-btn layer-btn-primary" onclick="layer.close(' + this.index + ')">关闭</button></div>' : ''}
        </div>
        <div class="layer-dialog-shade"></div>
      `;
      
      dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: ${this.index++};
        display: flex;
        align-items: center;
        justify-content: center;
      `;
      
      var content_div = dialog.querySelector('.layer-dialog-content');
      var area = options.area || ['400px', 'auto'];
      content_div.style.cssText = `
        background: white;
        border-radius: 4px;
        width: ${area[0]};
        height: ${area[1]};
        position: relative;
        z-index: 1;
        padding: 20px;
      `;
      
      var shade = dialog.querySelector('.layer-dialog-shade');
      shade.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.3);
      `;
      
      document.body.appendChild(dialog);
      
      return this.index - 1;
    }
  };
  
  // 导出到全局
  window.layer = layer;
  
})(window);
