# CSDN文章浏览/资源下载测试系统

这是一个用于测试CSDN文章内容获取功能的完整系统。

## 功能特性

- ✅ CSDN文章浏览和下载
- ✅ 邮件发送功能（模拟）
- ✅ 任务历史记录管理
- ✅ 支付功能（模拟）
- ✅ 响应式Web界面
- ✅ 实时任务状态更新

## 项目结构

```
csdn/
├── server.py              # Flask后端服务器
├── csdn-test.html         # 前端页面
├── requirements.txt       # Python依赖
├── css/                   # 样式文件
│   ├── bootstrap.min.css
│   ├── custom-theme.css
│   ├── font_3112205_f25la713bjb.css
│   └── layui.css
├── js/                    # JavaScript文件
│   ├── jquery.min.js
│   ├── bootstrap.min.js
│   ├── pace.min.js
│   ├── login.js
│   └── layui/
│       ├── layui.js
│       └── css/
│           └── layui.css
├── plugins/               # 插件文件
│   └── layer/
│       └── layer.js
└── images/                # 图片文件
    ├── WechatIMG5074.jpg
    └── placeholder.txt
```

## 安装和运行

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python server.py
```

### 3. 访问系统

打开浏览器访问：http://127.0.0.1:5000

## 使用说明

### 文章浏览功能

1. 选择"文章浏览"选项
2. 输入CSDN文章链接（例如：https://blog.csdn.net/username/article/details/123456）
3. 输入收件邮箱
4. 选择输出格式（HTML/Markdown）
5. 点击"下载"按钮

### 文件下载功能

1. 选择"文件代下载"选项
2. 输入CSDN文件下载链接
3. 输入收件邮箱
4. 点击"下载"按钮
5. 完成支付流程（模拟）

### 任务管理

- 查看任务历史记录
- 重试失败的任务
- 查看任务状态和进度

## API接口

### 1. 文章下载接口

```
POST /article/downloadnew
Content-Type: application/json

{
    "link": "文章URL",
    "email": "收件邮箱",
    "format": "html",
    "token": "用户token"
}
```

### 2. 任务历史接口

```
POST /csdn/article/csdnTaskDownloadHistorylist
Content-Type: application/json

{
    "token": "用户token",
    "page": 1,
    "pageSize": 10
}
```

### 3. 重试任务接口

```
POST /csdn/article/retryTask
Content-Type: application/json

{
    "token": "用户token",
    "id": "任务ID"
}
```

### 4. 支付二维码接口

```
POST /payment/getQRCode
Content-Type: application/json

{
    "token": "用户token",
    "id": "任务ID"
}
```

## 技术栈

### 后端
- Python 3.7+
- Flask 2.3.3
- Flask-CORS 4.0.0
- BeautifulSoup4 4.12.2
- Requests 2.31.0

### 前端
- HTML5 + CSS3
- JavaScript (ES6)
- Bootstrap 5 (简化版)
- Layui (简化版)
- Layer.js (简化版)
- jQuery (简化版)

## 注意事项

1. **这是测试版本**：所有功能都是模拟实现，不会真正发送邮件或进行支付
2. **数据存储**：任务数据存储在内存中，重启服务器后会丢失
3. **文章解析**：目前返回模拟数据，实际项目中需要实现真正的CSDN爬虫
4. **邮件发送**：需要配置真实的邮件服务器才能发送邮件
5. **支付功能**：需要集成真实的支付接口

## 扩展功能

如果要将此测试系统扩展为生产系统，需要：

1. **数据库集成**：使用MySQL/PostgreSQL存储任务数据
2. **真实爬虫**：实现CSDN文章解析功能
3. **邮件服务**：配置SMTP服务器
4. **支付集成**：接入微信支付/支付宝
5. **用户系统**：实现真实的用户注册登录
6. **VIP账号池**：管理CSDN VIP账号
7. **任务队列**：使用Celery处理后台任务
8. **监控日志**：添加系统监控和日志记录

## 故障排除

### 常见问题

1. **端口被占用**：修改server.py中的端口号
2. **依赖安装失败**：检查Python版本和网络连接
3. **页面无法访问**：检查防火墙设置
4. **JavaScript错误**：检查浏览器控制台错误信息

### 调试模式

服务器默认运行在调试模式，会显示详细的错误信息。生产环境请关闭调试模式。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱：<EMAIL>
- 微信：your-wechat-id

---

**免责声明**：本系统仅用于学习和测试目的，请遵守相关网站的使用条款和法律法规。
