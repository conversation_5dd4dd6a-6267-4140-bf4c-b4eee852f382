#!/bin/bash

echo "========================================"
echo "CSDN测试系统启动脚本"
echo "========================================"
echo

echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3环境，请先安装Python 3.7+"
    exit 1
fi

python3 --version

echo
echo "检查依赖包..."
if ! python3 -c "import flask" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
else
    echo "依赖包已安装"
fi

echo
echo "启动CSDN测试服务器..."
echo "请访问 http://127.0.0.1:5000 使用系统"
echo "按 Ctrl+C 停止服务器"
echo

python3 server.py
